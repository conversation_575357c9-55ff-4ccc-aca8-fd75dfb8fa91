# 统一身份认证平台 - 静态网页版

这是一个基于原始统一身份认证平台页面设计的静态网页项目，完全复制了原页面的UI布局和交互效果。

## 项目特点

- ✅ **完全静态化**：纯HTML/CSS/JavaScript实现，无需后端服务器
- ✅ **UI高度还原**：完全复制原页面的视觉效果和布局
- ✅ **响应式设计**：支持桌面端和移动端适配
- ✅ **详细注释**：所有UI组件都有详细的中文注释，便于修改
- ✅ **模块化结构**：代码结构清晰，易于维护和扩展

## 项目结构

```
├── index.html              # 主页面文件
├── css/
│   └── style.css          # 样式文件（包含所有UI组件样式）
├── js/
│   └── script.js          # JavaScript功能文件（包含交互逻辑）
├── images/                # 图片资源文件夹
│   ├── logoNew.png        # 桌面版Logo
│   ├── logo.png           # 移动版Logo
│   ├── banner-1.jpg       # 背景轮播图1
│   ├── banner-2.jpg       # 背景轮播图2
│   ├── captcha.jpg        # 验证码图片
│   ├── qrcode-placeholder.png  # 二维码占位图
│   ├── l-username.png     # 用户名输入框图标
│   ├── l-password.png     # 密码输入框图标
│   └── code.png           # 验证码输入框图标
└── README.md              # 项目说明文档
```

## 主要功能模块

### 1. 头部区域 (Header)
- **Logo显示**：支持桌面版和移动版Logo切换
- **实时时间**：动态显示当前日期和时间
- **系统公告**：公告链接入口

### 2. 背景轮播区域 (Banner)
- **图片轮播**：自动切换背景图片，支持淡入淡出效果
- **响应式适配**：图片自适应不同屏幕尺寸

### 3. 登录表单区域 (Login Form)
- **用户名输入框**：支持学工号输入
- **密码输入框**：密码类型输入
- **验证码输入框**：带验证码图片，支持点击刷新
- **记住登录**：5天内自动登录选项
- **表单验证**：完整的前端验证逻辑
- **登录按钮**：带加载状态的登录按钮

### 4. 二维码登录区域 (QR Code)
- **二维码显示**：显示二维码占位图
- **扫码提示**：用户友好的扫码说明

### 5. 其他功能链接
- **忘记密码**：密码找回链接
- **账号激活**：账号激活链接

### 6. 页脚区域 (Footer)
- **版权信息**：显示版权和备案信息

## UI组件注释说明

### HTML结构注释
每个主要的HTML区块都有详细的注释：
```html
<!-- 头部区域 -->
<div class="header">
    <!-- 右侧时间和公告区域 -->
    <div class="header_right">
        <!-- 实时时间显示 -->
        <div class="header_time" id="currentTime">...</div>
    </div>
</div>
```

### CSS样式注释
所有CSS样式都有功能说明：
```css
/* 头部样式 */
.header {
    height: 9.4rem;
    background: white;
}

/* Logo区域样式 */
.logo_pic {
    float: left;
    padding: 3.3rem 0 2.1rem;
}
```

### JavaScript功能注释
每个函数都有详细的功能说明：
```javascript
/**
 * 更新当前时间显示
 */
function updateCurrentTime() {
    // 获取当前时间并格式化显示
}

/**
 * 处理登录表单提交
 * @returns {boolean} - 返回false阻止表单默认提交
 */
function handleLogin() {
    // 表单验证和登录处理逻辑
}
```

## 如何使用

### 1. 直接使用
直接用浏览器打开 `index.html` 文件即可查看效果。

### 2. 本地服务器运行
为了更好的体验，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

然后访问 `http://localhost:8000`

### 3. 部署到服务器
将所有文件上传到Web服务器即可。

## 自定义修改指南

### 修改Logo
替换 `images/` 文件夹中的Logo文件：
- `logoNew.png` - 桌面版Logo
- `logo.png` - 移动版Logo

### 修改背景图片
替换 `images/` 文件夹中的背景图片：
- `banner-1.jpg` - 背景图1
- `banner-2.jpg` - 背景图2

### 修改颜色主题
在 `css/style.css` 中修改主要颜色变量：
```css
/* 主色调 */
background-color: #1d31a0;  /* 蓝色主题 */
color: #fff;                /* 白色文字 */
```

### 修改登录逻辑
在 `js/script.js` 的 `handleLogin()` 函数中修改登录验证逻辑。

### 添加新功能
所有代码都有详细注释，可以根据注释说明添加新的功能模块。

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 技术栈

- **HTML5**：语义化标签
- **CSS3**：Flexbox布局、响应式设计、动画效果
- **JavaScript ES6+**：模块化编程、DOM操作

## 注意事项

1. **图片资源**：项目中的二维码使用占位图片，实际使用时需要替换为真实的二维码
2. **登录逻辑**：当前为演示版本，实际使用需要连接后端API
3. **安全性**：生产环境使用时需要添加适当的安全措施

## 更新日志

### v1.0.0 (2025-07-14)
- ✅ 完成基础UI布局
- ✅ 实现响应式设计
- ✅ 添加交互功能
- ✅ 完善代码注释

---

**项目说明**：此项目仅用于UI展示和学习目的，所有设计版权归原网站所有。
