# 部署指南

## 快速开始

### 方法1：直接打开文件
直接用浏览器打开 `index.html` 文件即可查看效果。

### 方法2：本地服务器（推荐）

#### 使用Python
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### 使用Node.js
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8000
```

#### 使用PHP
```bash
php -S localhost:8000
```

然后访问 `http://localhost:8000`

## 生产环境部署

### 1. 静态网站托管

#### GitHub Pages
1. 将代码推送到GitHub仓库
2. 在仓库设置中启用GitHub Pages
3. 选择主分支作为源

#### Netlify
1. 将项目文件夹拖拽到Netlify部署页面
2. 或连接GitHub仓库自动部署

#### Vercel
1. 使用Vercel CLI：`vercel --prod`
2. 或在Vercel网站上导入项目

### 2. 传统Web服务器

#### Apache
将所有文件上传到Apache的`htdocs`或`public_html`目录

#### Nginx
将所有文件上传到Nginx配置的网站根目录

#### IIS
将所有文件上传到IIS网站根目录

## 自定义配置

### 修改登录验证
在 `js/script.js` 的 `handleLogin()` 函数中修改：

```javascript
// 当前演示代码
if (username === 'admin' && password === 'admin' && authcode === '1234') {
    showSuccess('登录成功！');
} else {
    showError('用户名、密码或验证码错误，请重新输入');
}

// 修改为实际的API调用
fetch('/api/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        username: username,
        password: password,
        authcode: authcode
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        showSuccess('登录成功！');
        window.location.href = data.redirectUrl;
    } else {
        showError(data.message);
    }
});
```

### 修改二维码
替换 `index.html` 中的二维码区域：

```html
<!-- 当前占位代码 -->
<div class="qrcode-placeholder">
    <!-- CSS绘制的二维码样式 -->
</div>

<!-- 修改为实际二维码 -->
<img src="/api/qrcode" alt="二维码" id="qrcode-img">
<script>
// 定期刷新二维码
setInterval(() => {
    document.getElementById('qrcode-img').src = '/api/qrcode?t=' + Date.now();
}, 30000);
</script>
```

### 修改样式主题
在 `css/style.css` 中修改主要颜色：

```css
/* 主色调变量 */
:root {
    --primary-color: #1d31a0;    /* 主蓝色 */
    --primary-hover: #1a2d8f;    /* 悬停蓝色 */
    --text-color: #333;          /* 文字颜色 */
    --background-color: #f5f5f5;  /* 背景色 */
}

/* 使用变量 */
.login_btn {
    background-color: var(--primary-color);
}

.login_btn:hover {
    background-color: var(--primary-hover);
}
```

## 性能优化

### 1. 图片优化
- 压缩背景图片文件大小
- 使用WebP格式（如果支持）
- 添加图片懒加载

### 2. CSS优化
- 压缩CSS文件
- 移除未使用的样式
- 使用CSS变量统一管理颜色

### 3. JavaScript优化
- 压缩JS文件
- 使用事件委托减少事件监听器
- 添加防抖处理

### 4. 缓存策略
添加适当的HTTP缓存头：

```apache
# Apache .htaccess
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>
```

## 安全考虑

### 1. HTTPS
生产环境必须使用HTTPS

### 2. 内容安全策略(CSP)
添加CSP头部：

```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
```

### 3. 输入验证
加强前端输入验证：

```javascript
function validateInput(input) {
    // 移除危险字符
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
}
```

## 浏览器兼容性

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 兼容性处理
如需支持更老的浏览器，可以添加polyfill：

```html
<script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
```

## 监控和分析

### 添加Google Analytics
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### 错误监控
```javascript
window.addEventListener('error', function(e) {
    // 发送错误信息到监控服务
    console.error('页面错误:', e.error);
});
```

## 故障排除

### 常见问题

1. **页面样式不显示**
   - 检查CSS文件路径是否正确
   - 确认服务器正确提供CSS文件

2. **图片不显示**
   - 检查图片文件路径
   - 确认图片文件存在

3. **JavaScript功能不工作**
   - 打开浏览器开发者工具查看错误
   - 检查JS文件路径

4. **移动端显示异常**
   - 检查viewport meta标签
   - 测试响应式CSS规则

### 调试技巧
- 使用浏览器开发者工具
- 检查网络请求状态
- 查看控制台错误信息
- 使用移动设备模拟器测试

---

如有问题，请查看项目README.md文件或提交Issue。
