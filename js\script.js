/**
 * 统一身份认证平台 - JavaScript功能模块
 * 包含登录表单处理、时间显示、验证码刷新等功能
 */

// 页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面功能
 */
function initializePage() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000); // 每秒更新时间
    setupFormValidation();
    setupImageCarousel();
}

/**
 * 更新当前时间显示
 */
function updateCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    // 获取星期
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = weekdays[now.getDay()];
    
    const timeString = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds} (${weekday})`;
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * 切换登录方式（密码登录/二维码登录）
 * @param {string} type - 登录类型：'password' 或 'qrcode'
 */
function switchLoginType(type) {
    const passwordTab = document.querySelector('.login_header span.left');
    const passwordForm = document.querySelector('.login_form1');
    const qrcodeForm = document.querySelector('.form-qr-code');
    
    if (type === 'password') {
        // 切换到密码登录
        passwordTab.classList.add('active');
        if (passwordForm) passwordForm.style.display = 'block';
        if (qrcodeForm) qrcodeForm.style.display = 'none';
    } else if (type === 'qrcode') {
        // 切换到二维码登录
        passwordTab.classList.remove('active');
        if (passwordForm) passwordForm.style.display = 'none';
        if (qrcodeForm) qrcodeForm.style.display = 'flex';
    }
}

/**
 * 刷新验证码图片
 */
function refreshCaptcha() {
    const captchaImg = document.querySelector('.codeImg');
    if (captchaImg) {
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        captchaImg.src = `images/captcha.jpg?t=${timestamp}`;
        
        // 添加刷新动画效果
        captchaImg.style.opacity = '0.5';
        setTimeout(() => {
            captchaImg.style.opacity = '1';
        }, 200);
    }
}

/**
 * 处理登录表单提交
 * @returns {boolean} - 返回false阻止表单默认提交
 */
function handleLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    const authcode = document.getElementById('authcode').value.trim();
    const errorElement = document.getElementById('errorMessage');
    
    // 清除之前的错误信息
    errorElement.textContent = '';
    
    // 表单验证
    if (!username) {
        showError('请输入学工号');
        document.getElementById('username').focus();
        return false;
    }
    
    if (!password) {
        showError('请输入密码');
        document.getElementById('password').focus();
        return false;
    }
    
    if (!authcode) {
        showError('请输入验证码');
        document.getElementById('authcode').focus();
        return false;
    }
    
    // 模拟登录处理
    showLoading();
    
    // 模拟网络请求延迟
    setTimeout(() => {
        hideLoading();
        
        // 这里可以添加实际的登录逻辑
        // 目前只是演示，显示成功消息
        if (username === 'admin' && password === 'admin' && authcode === '1234') {
            showSuccess('登录成功！');
            // 可以在这里跳转到其他页面
            // window.location.href = 'dashboard.html';
        } else {
            showError('用户名、密码或验证码错误，请重新输入');
            refreshCaptcha(); // 刷新验证码
        }
    }, 1500);
    
    return false; // 阻止表单默认提交
}

/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
function showError(message) {
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.color = '#ff2424';
    }
}

/**
 * 显示成功信息
 * @param {string} message - 成功信息
 */
function showSuccess(message) {
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.color = '#28a745';
    }
}

/**
 * 显示加载状态
 */
function showLoading() {
    const loginBtn = document.querySelector('.login_btn');
    if (loginBtn) {
        loginBtn.value = '登录中...';
        loginBtn.disabled = true;
        loginBtn.style.opacity = '0.7';
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loginBtn = document.querySelector('.login_btn');
    if (loginBtn) {
        loginBtn.value = '登 录';
        loginBtn.disabled = false;
        loginBtn.style.opacity = '1';
    }
}

/**
 * 设置表单验证
 */
function setupFormValidation() {
    const form = document.getElementById('loginForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });
    }
    
    // 输入框获得焦点时清除错误信息
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            const errorElement = document.getElementById('errorMessage');
            if (errorElement) {
                errorElement.textContent = '';
            }
        });
    });
    
    // 回车键提交表单
    inputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });
    });
}

/**
 * 设置背景图片轮播
 */
function setupImageCarousel() {
    const bannerLists = document.querySelector('.banner-pic-lists');
    const bannerItems = document.querySelectorAll('.banner-pic-list');
    
    if (bannerItems.length > 1) {
        let currentIndex = 0;
        
        // 设置轮播
        setInterval(() => {
            currentIndex = (currentIndex + 1) % bannerItems.length;
            
            // 简单的淡入淡出效果
            bannerItems.forEach((item, index) => {
                if (index === currentIndex) {
                    item.style.opacity = '1';
                    item.style.zIndex = '1';
                } else {
                    item.style.opacity = '0';
                    item.style.zIndex = '0';
                }
            });
        }, 5000); // 每5秒切换一次
        
        // 初始化样式
        bannerItems.forEach((item, index) => {
            item.style.position = 'absolute';
            item.style.top = '0';
            item.style.left = '0';
            item.style.transition = 'opacity 1s ease-in-out';
            
            if (index === 0) {
                item.style.opacity = '1';
                item.style.zIndex = '1';
            } else {
                item.style.opacity = '0';
                item.style.zIndex = '0';
            }
        });
    }
}

/**
 * 处理记住登录状态
 */
function handleRememberMe() {
    const rememberCheckbox = document.getElementById('rememberMe');
    const username = document.getElementById('username').value;
    
    if (rememberCheckbox && rememberCheckbox.checked && username) {
        // 将用户名保存到localStorage（实际项目中应该加密处理）
        localStorage.setItem('rememberedUsername', username);
    } else {
        localStorage.removeItem('rememberedUsername');
    }
}

/**
 * 页面加载时恢复记住的用户名
 */
function restoreRememberedUsername() {
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    const usernameInput = document.getElementById('username');
    const rememberCheckbox = document.getElementById('rememberMe');
    
    if (rememberedUsername && usernameInput) {
        usernameInput.value = rememberedUsername;
        if (rememberCheckbox) {
            rememberCheckbox.checked = true;
        }
    }
}

// 页面加载时恢复记住的用户名
document.addEventListener('DOMContentLoaded', restoreRememberedUsername);
