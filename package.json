{"name": "unified-auth-platform", "version": "1.0.0", "description": "统一身份认证平台 - 带数据库功能", "main": "server.js", "scripts": {"start": "node server.js", "stop": "taskkill /F /IM node.exe", "dev": "nodemon server.js", "static": "http-server -p 8001 -o", "build": "echo '项目已准备就绪'", "serve": "python -m http.server 8001"}, "keywords": ["login", "authentication", "database", "sqlite", "html", "css", "javascript", "nodejs"], "author": "Developer", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "cors": "^2.8.5"}, "devDependencies": {"http-server": "^14.1.1", "nodemon": "^3.0.1"}, "repository": {"type": "git", "url": "."}, "homepage": ".", "browserslist": ["> 1%", "last 2 versions", "not dead"]}