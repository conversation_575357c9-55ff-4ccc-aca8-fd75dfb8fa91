/* 基础重置样式 */
* {
    box-sizing: border-box;
}

html, body, div, p, span, ul, li, img, i, h1, h2, h3, h4, h5, h6, a, input, button {
    padding: 0;
    margin: 0;
}

/* 响应式字体大小设置 */
html {
    font-size: 14px;
}

@media screen and (min-width: 321px) and (max-width: 375px) {
    html { font-size: 11px; }
}

@media screen and (min-width: 376px) and (max-width: 414px) {
    html { font-size: 12px; }
}

@media screen and (min-width: 415px) and (max-width: 639px) {
    html { font-size: 15px; }
}

/* 基础样式 */
img {
    border: 0;
}

body {
    font-family: "Microsoft YaHei", Arial, sans-serif;
    height: 100vh;
    background-color: #f5f5f5;
    overflow-x: hidden; /* 防止水平滚动 */
    width: 100%;
}

input, button {
    outline: 0;
    border: 0;
    font-size: 16px;
}

input:-webkit-autofill {
    background-color: white;
}

li {
    list-style: none;
}

a, a:hover, a:visited, a:active {
    text-decoration: none;
    border-color: #1D319E;
}

/* 头部样式 */
.header {
    height: 9.4rem;
    background: white;
}

.center {
    height: 100%;
    padding: 0 9rem 0 10rem;
}

/* Logo区域样式 */
.logo_pic {
    float: left;
    padding: 3.3rem 0 2.1rem;
}

.logo {
    display: block;
    height: 4rem;
}

.logo-mobile {
    display: none;
}

/* 头部右侧区域样式 */
.header_right {
    float: right;
}

.header_time {
    margin-top: 4.5rem;
    text-align: center;
    line-height: 2.7rem;
    background-color: #1d31a0;
    color: white;
    font-size: max(12px, .8rem);
    padding: 0 1.4rem;
    border-radius: .75rem;
}

.header_gonggao {
    display: none;
    text-align: right;
    padding-top: .85rem;
}

.header_gonggao a {
    padding-left: 1.4rem;
    font-size: max(12px, .8rem);
    color: #0571bc;
}

/* 主容器样式 */
.container {
    width: 100%;
    height: calc(100vh - 8.4rem - 9.4rem);
}

/* 背景轮播区域样式 */
.banner {
    position: relative;
    width: 100%;
}

.banner-see {
    width: 100%;
    overflow: hidden;
    position: relative;
    min-height: 20rem;
    height: calc(100vh - 9.4rem - 8.4rem) !important;
}

.banner-pic-lists {
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    width: 100%;
}

.banner-pic-list {
    height: 100%;
    width: 100%;
}

.banner-pic-list img {
    display: block;
    width: 100%;
    height: calc(100vh - 9.4rem - 8.4rem) !important;
    min-height: 40rem;
    object-fit: cover;
}

/* 登录区域样式 */
.login {
    position: absolute;
    top: calc((100vh - 9.4rem - 8.4rem)/8);
    right: 7.1rem;
    z-index: 2;
    display: flex;
    background: rgba(0, 0, 0, 0.5);
    border-radius: .75rem;
}

.login_seen {
    padding: 1rem 2.5rem;
    border-radius: 25px 0 0 25px;
    width: 16.8rem;
    padding: 0rem 3.85rem 0 2.7rem;
    text-align: center;
}

.mobilelogo {
    display: none;
}

/* 登录标题样式 */
.login_header {
    color: #fff;
    font-weight: bold;
    font-size: max(12px, 1rem);
    padding: 2.2rem 0 1.45rem;
    letter-spacing: 2px;
}

.login_header span {
    cursor: pointer;
    display: inline-block;
    border-bottom: 0.15rem solid transparent;
    margin-bottom: -0.2rem;
    user-select: none;
}

.login_header span:first-child {
    margin-right: 1rem;
}

.login_header span.active {
    border-color: #fff;
}

/* 表单输入框样式 */
.fill_form {
    margin-bottom: .75rem;
    padding: 0.25rem .75rem;
    background-color: #f6f6f6;
    border-radius: 0.2rem;
    overflow: auto;
    display: flex;
}

.fill_form input {
    font-size: max(12px, .75rem);
    background-color: #f6f6f6;
    display: block;
    float: left;
    line-height: 1.6rem;
    height: 1.6rem;
    width: 100%;
}

.fill_form_other {
    float: left;
    line-height: 1.6rem;
    height: 1.6rem;
    overflow: hidden;
    width: 1.5rem;
}

/* 输入框图标样式 - 使用CSS绘制图标 */
.fill_form_other1 {
    position: relative;
}

.fill_form_other1::before {
    content: "👤";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #666;
}

.fill_form_other2 {
    position: relative;
}

.fill_form_other2::before {
    content: "🔒";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #666;
}

.fill_form_other3 {
    position: relative;
}

.fill_form_other3::before {
    content: "🔢";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #666;
}

/* 验证码区域样式 */
.codeCon {
    display: flex;
}

.Verification {
    margin-right: .5rem;
}

.codeImg {
    height: 2.1rem;
    cursor: pointer;
}

.fill_form.code {
    margin-bottom: 5px;
}

/* 记住登录选项样式 */
.rememberdiv {
    text-align: left;
    color: #fff;
    margin: .6rem 0 1.25rem;
}

.rememberdiv span {
    cursor: pointer;
    font-size: max(12px, .6rem);
}

#rememberMe {
    cursor: pointer;
    margin-left: 0px;
    width: 13px;
    height: 13px;
    border-radius: 0;
}

/* 登录按钮样式 */
.login_btn {
    display: block;
    height: 2.1rem;
    text-align: center;
    color: white;
    width: 100%;
    font-size: max(12px, .9rem);
    background-color: #1d31a0;
    border-radius: .2rem;
    cursor: pointer;
}

.login_btn:hover {
    background-color: #1a2d8f;
}

/* 其他登录选项样式 */
.login_other {
    text-align: center;
    line-height: 2.1rem;
    position: relative;
    height: 2.1rem;
    font-size: max(12px, .5rem);
}

.login_other a {
    color: #fff;
}

.login_other a:hover {
    text-decoration: underline;
}

.login_other a:first-child {
    position: absolute;
    left: 0;
    top: 0;
}

.login_other a.last {
    position: absolute;
    right: 0;
    top: 0;
}

/* 错误信息样式 */
.form-error {
    text-align: left;
    color: #ff2424;
    font-size: max(12px, .75rem);
    margin-bottom: 0.25rem;
}

/* 二维码登录区域样式 */
.form-qr-code {
    display: flex !important;
    border-radius: 0 1.25rem 1.25rem 0;
    text-align: center;
    padding-right: 2.7rem;
}

.login_erweima {
    padding-bottom: 15px;
    overflow: hidden;
}

.login_erweima .qrcode-title {
    color: #1d31a0;
    text-align: center;
    font-weight: 600;
    color: #fff;
    font-size: max(12px, 1rem);
    padding: 2.2rem 0 1.45rem;
    letter-spacing: 2px;
}

.login_erweima p {
    text-align: center;
    color: #393939;
    font-size: max(12px, .7rem);
    margin-top: .5rem;
    color: #fff;
}

/* 二维码占位图样式 */
.qrcode-placeholder {
    width: 200px;
    height: 200px;
    margin: 20px auto;
    background: #fff;
    border: 2px solid #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.qr-pattern {
    width: 160px;
    height: 160px;
    position: relative;
    background:
        repeating-linear-gradient(
            0deg,
            #000 0px,
            #000 8px,
            #fff 8px,
            #fff 16px
        ),
        repeating-linear-gradient(
            90deg,
            #000 0px,
            #000 8px,
            #fff 8px,
            #fff 16px
        );
    background-size: 16px 16px;
    opacity: 0.8;
}

.qr-corner {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 4px solid #000;
}

.qr-corner::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: 16px;
    height: 16px;
    background: #000;
}

.qr-corner-tl {
    top: 8px;
    left: 8px;
}

.qr-corner-tr {
    top: 8px;
    right: 8px;
}

.qr-corner-bl {
    bottom: 8px;
    left: 8px;
}

/* 页脚样式 */
.footer {
    height: 8.4rem;
    color: #333;
    background-color: #f5f5f5; /* 确保页脚背景与页面背景一致 */
}

.footer_tishi {
    text-align: center;
    padding-top: 3.7rem;
    letter-spacing: .1rem;
    font-size: max(12px, .5rem);
}

/* 页脚文字样式 */
.copyright-text {
    color: #333;
    font-size: max(14px, 1rem);
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: transparent; /* 移除白色背景 */
}

.icp-text {
    color: #333;
    font-size: max(14px, 1rem);
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: transparent;
}

.footer_tishi a {
    color: #333;
    text-decoration: none;
}

.footer_tishi a:hover {
    color: #1d31a0;
    text-decoration: underline;
}

/* 响应式设计 */
@media screen and (max-width: 939px) {
    .center {
        padding: 0 1rem;
    }
}

@media screen and (max-width: 866px) {
    .logo_pic {
        width: calc(100% - 14.5rem);
    }
}

@media screen and (max-width: 520px) {
    /* 移动端完全隐藏头部，避免显示异常 */
    .header {
        display: none;
    }

    /* 移动端主容器优化 */
    .container {
        height: 100vh;
        min-height: 100vh;
        position: relative;
        padding: 0;
        margin: 0;
    }

    .banner-see {
        height: 100vh;
        min-height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1;
    }

    .banner-pic-list img {
        height: 100vh;
        min-height: 100vh;
        width: 100%;
        object-fit: cover;
        object-position: center;
    }

    /* 移动端登录区域完全重构 */
    .login {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 85%;
        max-width: 320px;
        display: block;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 0.8rem;
        padding: 0;
        z-index: 1000;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(10px);
    }

    .form-qr-code.login_form.login_form2 {
        display: none !important;
    }

    .login_seen {
        width: 100%;
        padding: 1rem;
        border-radius: 0.8rem;
        box-sizing: border-box;
    }

    .mobilelogo {
        display: block;
        text-align: center;
        padding: 0.6rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 0.8rem;
    }

    .mobilelogo img {
        width: 40px;
        height: 40px;
    }

    .mobilelogo .title {
        color: #fff;
        font-size: 0.85rem;
        margin-top: 0.3rem;
        font-weight: normal;
        opacity: 0.95;
    }


    /* 移动端登录表单优化 */
    .login_header {
        padding: 0.8rem 0 0.6rem;
        font-size: max(15px, 1rem);
        color: #fff;
    }

    .fill_form {
        margin-bottom: 0.8rem;
        padding: 0.5rem 0.8rem;
        border-radius: 0.4rem;
        background-color: rgba(246, 246, 246, 0.95);
    }

    .fill_form input {
        font-size: max(15px, 1rem);
        line-height: 2.2rem;
        height: 2.2rem;
        background-color: transparent;
        color: #333;
    }

    .fill_form input::placeholder {
        color: #666;
        font-size: max(14px, 0.9rem);
    }

    .fill_form_other {
        width: 2.2rem;
        line-height: 2.2rem;
        height: 2.2rem;
    }

    .codeCon {
        flex-direction: row;
        gap: 0.5rem;
        align-items: center;
    }

    .Verification {
        margin-right: 0;
        flex: 1;
    }

    .codeImg {
        height: 3rem;
        width: auto;
        max-width: 100px;
        border-radius: 0.3rem;
        border: 1px solid #ddd;
    }

    .rememberdiv {
        margin: 0.8rem 0;
        font-size: max(13px, 0.85rem);
        text-align: left;
    }

    .login_btn {
        height: 2.8rem;
        font-size: max(15px, 1rem);
        margin-top: 0.8rem;
        border-radius: 0.4rem;
        font-weight: 500;
    }

    .login_other {
        font-size: max(13px, 0.85rem);
        margin-top: 1rem;
        padding-top: 0.8rem;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
    }

    /* 移动端页脚样式优化 */
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        min-height: 2.5rem;
        padding: 0.3rem 0;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 500;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer_tishi {
        padding: 0.3rem 1rem;
        text-align: center;
    }

    .copyright-text,
    .icp-text {
        font-size: max(9px, 0.6rem);
        display: inline-block;
        margin: 0.05rem 0;
        color: rgba(255, 255, 255, 0.8);
    }

    /* 移动端页脚文字可能需要换行 */
    .footer_tishi p {
        line-height: 1.2;
        word-wrap: break-word;
        margin: 0.1rem 0;
    }
}

@media screen and (max-width: 1440px) {
    .login {
        max-height: 25.3rem;
    }
}

@media screen and (max-height: 700px) {
    .login {
        max-height: 22.8rem;
    }
}

@media screen and (max-width: 1300px) {
    .login {
        max-height: 25.3rem;
    }
}

/* 平板设备优化 */
@media screen and (min-width: 521px) and (max-width: 768px) {
    .login {
        right: 2rem;
        max-width: 400px;
    }

    .center {
        padding: 0 2rem;
    }
}

/* 超小屏幕设备优化 */
@media screen and (max-width: 320px) {
    html {
        font-size: 12px;
    }

    .login {
        margin: 1rem auto;
        max-width: 280px;
    }

    .login_seen {
        padding: 1rem;
    }

    .mobilelogo img {
        width: 40px;
        height: 40px;
    }

    .mobilelogo .title {
        font-size: 0.9rem;
    }

    .fill_form {
        padding: 0.3rem 0.6rem;
    }

    .fill_form input {
        font-size: 14px;
    }

    .login_btn {
        height: 2.2rem;
        font-size: 14px;
    }
}

/* 横屏模式优化 */
@media screen and (max-height: 600px) and (orientation: landscape) {
    .login {
        margin: 1rem auto;
        max-height: none;
    }

    .mobilelogo {
        padding: 0.5rem 0;
    }

    .login_header {
        padding: 0.5rem 0;
    }

    .footer {
        min-height: 3rem;
    }
}
