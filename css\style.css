/* 基础重置样式 */
* {
    box-sizing: border-box;
}

html, body, div, p, span, ul, li, img, i, h1, h2, h3, h4, h5, h6, a, input, button {
    padding: 0;
    margin: 0;
}

/* 响应式字体大小设置 */
html {
    font-size: 14px;
}

@media screen and (min-width: 321px) and (max-width: 375px) {
    html { font-size: 11px; }
}

@media screen and (min-width: 376px) and (max-width: 414px) {
    html { font-size: 12px; }
}

@media screen and (min-width: 415px) and (max-width: 639px) {
    html { font-size: 15px; }
}

/* 基础样式 */
img {
    border: 0;
}

body {
    font-family: "Microsoft YaHei", Arial, sans-serif;
    height: 100vh;
    background-color: #f5f5f5;
    overflow-x: hidden; /* 防止水平滚动 */
    width: 100%;
}

input, button {
    outline: 0;
    border: 0;
    font-size: 16px;
}

input:-webkit-autofill {
    background-color: white;
}

li {
    list-style: none;
}

a, a:hover, a:visited, a:active {
    text-decoration: none;
    border-color: #1D319E;
}

/* 头部样式 */
.header {
    height: 9.4rem;
    background: white;
}

.center {
    height: 100%;
    padding: 0 9rem 0 10rem;
}

/* Logo区域样式 */
.logo_pic {
    float: left;
    padding: 3.3rem 0 2.1rem;
}

.logo {
    display: block;
    height: 4rem;
}

.logo-mobile {
    display: none;
}

/* 头部右侧区域样式 */
.header_right {
    float: right;
}

.header_time {
    margin-top: 4.5rem;
    text-align: center;
    line-height: 2.7rem;
    background-color: #1d31a0;
    color: white;
    font-size: max(12px, .8rem);
    padding: 0 1.4rem;
    border-radius: .75rem;
}

.header_gonggao {
    display: none;
    text-align: right;
    padding-top: .85rem;
}

.header_gonggao a {
    padding-left: 1.4rem;
    font-size: max(12px, .8rem);
    color: #0571bc;
}

/* 主容器样式 */
.container {
    width: 100%;
    height: calc(100vh - 8.4rem - 9.4rem);
}

/* 背景轮播区域样式 */
.banner {
    position: relative;
    width: 100%;
}

.banner-see {
    width: 100%;
    overflow: hidden;
    position: relative;
    min-height: 20rem;
    height: calc(100vh - 9.4rem - 8.4rem) !important;
}

.banner-pic-lists {
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    width: 100%;
}

.banner-pic-list {
    height: 100%;
    width: 100%;
}

.banner-pic-list img {
    display: block;
    width: 100%;
    height: calc(100vh - 9.4rem - 8.4rem) !important;
    min-height: 40rem;
    object-fit: cover;
}

/* 登录区域样式 - 电脑端优化 */
.login {
    position: absolute;
    top: calc((100vh - 9.4rem - 8.4rem)/8);
    right: 5rem;
    z-index: 2;
    display: flex;
    background: rgba(0, 0, 0, 0.5);
    border-radius: .75rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.login_seen {
    padding: 1rem 2.5rem;
    border-radius: 25px 0 0 25px;
    width: 25rem;
    padding: 0rem 3.5rem 0 3rem;
    text-align: center;
}

.mobilelogo {
    display: none;
}

/* 登录标题样式 */
.login_header {
    color: #fff;
    font-weight: bold;
    font-size: max(12px, 1rem);
    padding: 2.2rem 0 1.45rem;
    letter-spacing: 2px;
}

.login_header span {
    cursor: pointer;
    display: inline-block;
    border-bottom: 0.15rem solid transparent;
    margin-bottom: -0.2rem;
    user-select: none;
}

.login_header span:first-child {
    margin-right: 1rem;
}

.login_header span.active {
    border-color: #fff;
}

/* 表单输入框样式 - 电脑端优化 */
.fill_form {
    margin-bottom: .75rem;
    padding: 0.5rem 1rem;
    background-color: #f6f6f6;
    border-radius: 0.4rem;
    overflow: auto;
    display: flex;
    align-items: center;
    border: 1px solid #e0e0e0;
    transition: border-color 0.3s ease;
}

.fill_form:focus-within {
    border-color: #1d31a0;
    box-shadow: 0 0 0 2px rgba(29, 49, 160, 0.1);
}

.fill_form input {
    font-size: max(14px, .9rem);
    background-color: #f6f6f6;
    display: block;
    float: left;
    line-height: 2.2rem;
    height: 2.2rem;
    width: 100%;
    border: none;
    outline: none;
    padding: 0 0.8rem;
    box-sizing: border-box;
}

.fill_form_other {
    float: left;
    line-height: 2.2rem;
    height: 2.2rem;
    overflow: hidden;
    width: 2.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}

/* 输入框图标样式 - 电脑端优化 */
.fill_form_other1 {
    position: relative;
}

.fill_form_other1::before {
    content: "👤";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #666;
}

.fill_form_other2 {
    position: relative;
}

.fill_form_other2::before {
    content: "🔒";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #666;
}







/* 记住登录选项样式 */
.rememberdiv {
    text-align: left;
    color: #fff;
    margin: .6rem 0 1.25rem;
}

.rememberdiv span {
    cursor: pointer;
    font-size: max(12px, .6rem);
}

#rememberMe {
    cursor: pointer;
    margin-left: 0px;
    width: 13px;
    height: 13px;
    border-radius: 0;
}

/* 登录按钮样式 - 电脑端优化 */
.login_btn {
    display: block;
    height: 2.8rem;
    text-align: center;
    color: white;
    width: 100%;
    font-size: max(14px, 1rem);
    background-color: #1d31a0;
    border-radius: 0.3rem;
    cursor: pointer;
    border: none;
    outline: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
    line-height: 2.8rem;
}

.login_btn:hover {
    background-color: #1a2d8f;
}

/* 其他登录选项样式 */
.login_other {
    text-align: center;
    line-height: 2.1rem;
    position: relative;
    height: 2.1rem;
    font-size: max(12px, .5rem);
}

.login_other a {
    color: #fff;
}

.login_other a:hover {
    text-decoration: underline;
}

.login_other a:first-child {
    position: absolute;
    left: 0;
    top: 0;
}

.login_other a.last {
    position: absolute;
    right: 0;
    top: 0;
}

/* 错误信息样式 */
.form-error {
    text-align: left;
    color: #ff2424;
    font-size: max(12px, .75rem);
    margin-bottom: 0.25rem;
}

/* 二维码登录区域样式 - 精确对齐登录框 */
.form-qr-code {
    display: flex !important;
    border-radius: 0 0.75rem 0.75rem 0;
    text-align: center;
    padding: 0 2rem 0 1rem;
    width: 260px;
    align-items: flex-start;
    justify-content: center;
    margin-top: 0.5rem;
}

.login_erweima {
    padding: 0;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 0.3rem;
}

.login_erweima .qrcode-title {
    color: #fff;
    text-align: center;
    font-weight: 600;
    font-size: max(14px, 1rem);
    padding: 1.8rem 0 1rem;
    letter-spacing: 2px;
    margin: 0;
}

.login_erweima p {
    text-align: center;
    color: #fff;
    font-size: max(12px, .75rem);
    margin: 0.8rem 0 0 0;
    opacity: 0.9;
}

/* 二维码占位图样式 - 精确对齐 */
.qrcode-placeholder {
    width: 170px;
    height: 170px;
    margin: 0.2rem auto 0.8rem auto;
    background: #fff;
    border: 2px solid #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.qr-pattern {
    width: 130px;
    height: 130px;
    position: relative;
    background:
        repeating-linear-gradient(
            0deg,
            #000 0px,
            #000 6px,
            #fff 6px,
            #fff 12px
        ),
        repeating-linear-gradient(
            90deg,
            #000 0px,
            #000 6px,
            #fff 6px,
            #fff 12px
        );
    background-size: 16px 16px;
    opacity: 0.8;
}

.qr-corner {
    position: absolute;
    width: 32px;
    height: 32px;
    border: 3px solid #000;
}

.qr-corner::after {
    content: '';
    position: absolute;
    top: 6px;
    left: 6px;
    width: 12px;
    height: 12px;
    background: #000;
}

.qr-corner-tl {
    top: 8px;
    left: 8px;
}

.qr-corner-tr {
    top: 8px;
    right: 8px;
}

.qr-corner-bl {
    bottom: 8px;
    left: 8px;
}

/* 页脚样式 */
.footer {
    height: 8.4rem;
    color: #333;
    background-color: #f5f5f5; /* 确保页脚背景与页面背景一致 */
}

.footer_tishi {
    text-align: center;
    padding-top: 3.7rem;
    letter-spacing: .1rem;
    font-size: max(12px, .5rem);
}

/* 页脚文字样式 */
.copyright-text {
    color: #333;
    font-size: max(14px, 1rem);
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: transparent; /* 移除白色背景 */
}

.icp-text {
    color: #333;
    font-size: max(14px, 1rem);
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: transparent;
}

.footer_tishi a {
    color: #333;
    text-decoration: none;
}

.footer_tishi a:hover {
    color: #1d31a0;
    text-decoration: underline;
}

/* 响应式设计 */
@media screen and (max-width: 939px) {
    .center {
        padding: 0 1rem;
    }
}

@media screen and (max-width: 866px) {
    .logo_pic {
        width: calc(100% - 14.5rem);
    }
}

@media screen and (max-width: 520px) {
    /* 移动端完全隐藏头部和时间显示 */
    .header {
        display: none !important;
    }

    .header_time {
        display: none !important;
    }

    /* 隐藏所有可能的时间显示元素 */
    .time-display,
    .header_right,
    .logo_pic {
        display: none !important;
    }

    /* 移动端主容器优化 */
    .container {
        height: 100vh;
        min-height: 100vh;
        position: relative;
        padding: 0;
        margin: 0;
    }

    .banner-see {
        height: 100vh;
        min-height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1;
    }

    .banner-pic-list img {
        height: 100vh;
        min-height: 100vh;
        width: 100%;
        object-fit: cover;
        object-position: center;
    }

    /* 移动端登录区域完全重构 - 完美居中 */
    .login {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 88% !important;
        max-width: 340px !important;
        min-height: auto !important;
        display: block !important;
        background: rgba(0, 0, 0, 0.88) !important;
        border-radius: 0.8rem !important;
        padding: 0 !important;
        margin: 0 !important;
        z-index: 9999 !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.7) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .form-qr-code.login_form.login_form2 {
        display: none !important;
    }

    .login_seen {
        width: 100% !important;
        padding: 1.2rem !important;
        border-radius: 0.8rem !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        background: transparent !important;
    }

    /* 移动端完全隐藏Logo区域 */
    .mobilelogo {
        display: none !important;
    }

    .mobilelogo img {
        display: none !important;
    }

    .mobilelogo .title {
        display: none !important;
    }

    /* 隐藏左上角所有Logo相关元素 */
    .logo_pic,
    .logo-container,
    .header_logo {
        display: none !important;
    }

    /* 移动端隐藏二维码登录相关元素 */
    .qr-code,
    .qr-login,
    .qrcode-container,
    .scan-login,
    .wechat-login,
    .qr-switch,
    .login-switch,
    .tab-qr,
    .qr-tab {
        display: none !important;
    }

    /* 确保只显示密码登录表单 */
    .password-login,
    .login-form,
    .form-container {
        display: block !important;
    }

    /* 移动端输入框优化 */
    .login input[type="text"],
    .login input[type="password"] {
        width: 100% !important;
        height: 2.8rem !important;
        font-size: 0.9rem !important;
        padding: 0 0.8rem !important;
        margin-bottom: 0.8rem !important;
        border-radius: 0.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        background: rgba(255, 255, 255, 0.1) !important;
        color: #fff !important;
        box-sizing: border-box !important;
    }

    .login input::placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* 移动端登录按钮优化 */
    .login button,
    .login .login-btn {
        width: 100% !important;
        height: 2.8rem !important;
        font-size: 1rem !important;
        font-weight: bold !important;
        border-radius: 0.5rem !important;
        margin-top: 0.5rem !important;
    }


    /* 移动端登录表单完全优化 */
    .login_header {
        padding: 1rem 0 0.8rem !important;
        font-size: 1.1rem !important;
        color: #fff !important;
        text-align: center !important;
        font-weight: 500 !important;
        margin: 0 !important;
    }

    .fill_form {
        margin-bottom: 0.8rem !important;
        padding: 0 !important;
        border-radius: 0.5rem !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        overflow: hidden !important;
    }

    .fill_form input {
        font-size: 0.95rem !important;
        line-height: 2.5rem !important;
        height: 2.5rem !important;
        padding: 0 0.8rem !important;
        background: transparent !important;
        border: none !important;
        color: #fff !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .fill_form input::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
    }



    .login .login-btn,
    .login button[type="submit"] {
        background: #1976d2 !important;
        color: #fff !important;
        border: none !important;
        cursor: pointer !important;
        transition: background-color 0.3s !important;
    }

    .login .login-btn:hover,
    .login button[type="submit"]:hover {
        background: #1565c0 !important;
    }

    /* 移动端底部链接优化 */
    .login .login-footer {
        text-align: center !important;
        margin-top: 1rem !important;
        padding-top: 0.8rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .login .login-footer a {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 0.85rem !important;
        text-decoration: none !important;
        margin: 0 0.5rem !important;
    }

    .fill_form input::placeholder {
        color: #666;
        font-size: max(14px, 0.9rem);
    }

    .fill_form_other {
        width: 2.2rem;
        line-height: 2.2rem;
        height: 2.2rem;
    }



    .rememberdiv {
        margin: 0.8rem 0;
        font-size: max(13px, 0.85rem);
        text-align: left;
    }

    .login_btn {
        height: 2.8rem;
        font-size: max(15px, 1rem);
        margin-top: 0.8rem;
        border-radius: 0.4rem;
        font-weight: 500;
    }

    .login_other {
        font-size: max(13px, 0.85rem);
        margin-top: 1rem;
        padding-top: 0.8rem;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
    }

    /* 移动端页脚样式优化 */
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        min-height: 2.5rem;
        padding: 0.3rem 0;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 500;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer_tishi {
        padding: 0.3rem 1rem;
        text-align: center;
    }

    .copyright-text,
    .icp-text {
        font-size: max(9px, 0.6rem);
        display: inline-block;
        margin: 0.05rem 0;
        color: rgba(255, 255, 255, 0.8);
    }

    /* 移动端页脚文字可能需要换行 */
    .footer_tishi p {
        line-height: 1.2;
        word-wrap: break-word;
        margin: 0.1rem 0;
    }
}

@media screen and (max-width: 1440px) {
    .login {
        max-height: 25.3rem;
    }
}

@media screen and (max-height: 700px) {
    .login {
        max-height: 22.8rem;
    }
}

@media screen and (max-width: 1300px) {
    .login {
        max-height: 25.3rem;
    }
}

/* 平板设备优化 */
@media screen and (min-width: 521px) and (max-width: 768px) {
    .login {
        right: 2rem;
        max-width: 400px;
    }

    .center {
        padding: 0 2rem;
    }
}

/* 超小屏幕设备优化 */
@media screen and (max-width: 320px) {
    html {
        font-size: 12px;
    }

    .login {
        margin: 1rem auto;
        max-width: 280px;
    }

    .login_seen {
        padding: 1rem;
    }

    .mobilelogo img {
        width: 40px;
        height: 40px;
    }

    .mobilelogo .title {
        font-size: 0.9rem;
    }

    .fill_form {
        padding: 0.3rem 0.6rem;
    }

    .fill_form input {
        font-size: 14px;
    }

    .login_btn {
        height: 2.2rem;
        font-size: 14px;
    }
}

/* 横屏模式优化 */
@media screen and (max-height: 600px) and (orientation: landscape) {
    .login {
        margin: 1rem auto;
        max-height: none;
    }

    .mobilelogo {
        padding: 0.5rem 0;
    }

    .login_header {
        padding: 0.5rem 0;
    }

    .footer {
        min-height: 3rem;
    }
}
