# 数据库查看说明

## 数据库文件位置
- 文件名：`login_records.db`
- 位置：项目根目录

## 数据库结构
```sql
CREATE TABLE login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL,
    password TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 本地查看方法

### 方法一：使用SQLite命令行工具
1. 下载并安装 SQLite 命令行工具
2. 在项目目录打开命令行
3. 运行：`sqlite3 login_records.db`
4. 查看所有记录：
   ```sql
   SELECT * FROM login_attempts ORDER BY timestamp DESC;
   ```

### 方法二：使用SQLite浏览器工具
1. 下载 DB Browser for SQLite (https://sqlitebrowser.org/)
2. 安装并打开软件
3. 点击"打开数据库"，选择 `login_records.db` 文件
4. 在"浏览数据"标签页查看 `login_attempts` 表

### 方法三：使用VS Code插件
1. 安装 SQLite Viewer 插件
2. 在VS Code中右键点击 `login_records.db` 文件
3. 选择"Open with SQLite Viewer"

## 常用SQL查询

### 查看所有记录
```sql
SELECT * FROM login_attempts ORDER BY timestamp DESC;
```

### 查看今天的记录
```sql
SELECT * FROM login_attempts 
WHERE DATE(timestamp) = DATE('now') 
ORDER BY timestamp DESC;
```

### 统计总记录数
```sql
SELECT COUNT(*) as total_records FROM login_attempts;
```

### 查看最近10条记录
```sql
SELECT * FROM login_attempts 
ORDER BY timestamp DESC 
LIMIT 10;
```

### 按用户名统计
```sql
SELECT username, COUNT(*) as login_count 
FROM login_attempts 
GROUP BY username 
ORDER BY login_count DESC;
```

## 数据安全说明
- 数据库文件仅存储在本地
- 没有远程访问接口
- 建议定期备份数据库文件
- 如需清空数据，可直接删除 `login_records.db` 文件
